# Database Configuration
MYSQL_ROOT_PASSWORD=root
MYSQL_DATABASE=company_db
MYSQL_USER=company
MYSQL_PASSWORD=Ukshati@123

# Next.js Configuration
TURBOPACK=0
NEXT_DISABLE_SWC_NATIVE=true

# JWT Secret for Authentication
JWT_SECRET=your-jwt-secret-key-here

# Email Configuration for Purchase Order Sending
# For Gmail, you need to:
# 1. Enable 2-factor authentication on your Google account
# 2. Generate an App Password (not your regular password)
# 3. Use the App Password as EMAIL_PASS
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# Instructions for Gmail Setup:
# 1. Go to Google Account settings
# 2. Security > 2-Step Verification (enable if not already)
# 3. Security > App passwords
# 4. Generate a new app password for "Mail"
# 5. Use that 16-character password as EMAIL_PASS
