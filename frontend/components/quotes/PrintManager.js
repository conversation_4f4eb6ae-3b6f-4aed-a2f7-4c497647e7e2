import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { jsPDF } from 'jspdf';
import autoTable from 'jspdf-autotable';
import { FaPrint, FaDownload, FaTimes } from 'react-icons/fa';

const PrintManager = ({ 
  categories, 
  selectedItems, 
  customerData, 
  projectData, 
  onClose, 
  showNotification 
}) => {
  const [selectedCategories, setSelectedCategories] = useState([]);
  const [printOptions, setPrintOptions] = useState({
    includeSeparateItems: true,
    includeCustomerInfo: true,
    includeProjectInfo: true,
    paperSize: 'a4'
  });

  const dripCategory = categories.find(cat => cat.category_name === 'Drip');
  const plumbingCategory = categories.find(cat => cat.category_name === 'Plumbing');
  const automationCategory = categories.find(cat => cat.category_name === 'Automation');

  const handleCategoryToggle = (categoryId) => {
    setSelectedCategories(prev => 
      prev.includes(categoryId) 
        ? prev.filter(id => id !== categoryId)
        : [...prev, categoryId]
    );
  };

  const generatePDF = (type) => {
    const doc = new jsPDF();
    let yPosition = 20;

    // Company Header
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(16);
    doc.text('Ukshati Technologies Pvt Ltd.', 20, yPosition);
    yPosition += 10;

    doc.setFontSize(10);
    doc.setFont('helvetica', 'normal');
    doc.text('2nd floor, Pramod Automobiles Bldg.', 20, yPosition);
    yPosition += 5;
    doc.text('Karangalpady, Mangalore - 575003', 20, yPosition);
    yPosition += 5;
    doc.text('Karnataka, Phone: +91 8861567365', 20, yPosition);
    yPosition += 15;

    // Customer and Project Info
    if (printOptions.includeCustomerInfo && customerData) {
      doc.setFont('helvetica', 'bold');
      doc.setFontSize(12);
      doc.text('Customer Information:', 20, yPosition);
      yPosition += 8;

      doc.setFont('helvetica', 'normal');
      doc.setFontSize(10);
      doc.text(`Name: ${customerData.name}`, 20, yPosition);
      yPosition += 5;
      doc.text(`Phone: ${customerData.phone}`, 20, yPosition);
      yPosition += 5;
      if (customerData.address) {
        doc.text(`Address: ${customerData.address}`, 20, yPosition);
        yPosition += 5;
      }
      yPosition += 10;
    }

    if (printOptions.includeProjectInfo && projectData) {
      doc.setFont('helvetica', 'bold');
      doc.setFontSize(12);
      doc.text('Project Information:', 20, yPosition);
      yPosition += 8;

      doc.setFont('helvetica', 'normal');
      doc.setFontSize(10);
      doc.text(`Project: ${projectData.name}`, 20, yPosition);
      yPosition += 5;
      doc.text(`Status: ${projectData.status}`, 20, yPosition);
      yPosition += 15;
    }

    // Generate content based on type
    switch (type) {
      case 'drip':
        generateCategoryPDF(doc, dripCategory, yPosition);
        break;
      case 'plumbing':
        generateCategoryPDF(doc, plumbingCategory, yPosition);
        break;
      case 'automation':
        generateCategoryPDF(doc, automationCategory, yPosition);
        break;
      case 'drip-plumbing':
        generateCombinedPDF(doc, [dripCategory, plumbingCategory], yPosition);
        break;
      case 'custom':
        generateCustomPDF(doc, selectedCategories, yPosition);
        break;
      default:
        break;
    }

    // Save PDF
    const fileName = `${type}_${new Date().toISOString().split('T')[0]}.pdf`;
    doc.save(fileName);
    showNotification(`${type} PDF generated successfully`, 'success');
  };

  const generateCategoryPDF = (doc, category, startY) => {
    if (!category) return;

    const items = selectedItems[category.category_id] || [];
    const regularItems = items.filter(item => !item.printSeparately);
    const separateItems = items.filter(item => item.printSeparately);

    let yPosition = startY;

    // Category Title
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(14);
    doc.text(`${category.category_name} Items`, 20, yPosition);
    yPosition += 15;

    // Regular Items Table
    if (regularItems.length > 0) {
      const tableData = regularItems.map(item => [
        item.item_name,
        item.quantity.toString(),
        `₹${item.cost.toFixed(2)}`,
        `₹${(item.cost * item.quantity).toFixed(2)}`
      ]);

      autoTable(doc, {
        startY: yPosition,
        head: [['Item Name', 'Quantity', 'Unit Price', 'Total']],
        body: tableData,
        theme: 'grid',
        styles: { fontSize: 10 },
        headStyles: { fillColor: [66, 139, 202] }
      });

      yPosition = doc.lastAutoTable.finalY + 15;
    }

    // Separate Items
    if (printOptions.includeSeparateItems && separateItems.length > 0) {
      doc.setFont('helvetica', 'bold');
      doc.setFontSize(12);
      doc.text('Additional Items (Printed Separately):', 20, yPosition);
      yPosition += 10;

      const separateTableData = separateItems.map(item => [
        item.item_name,
        item.quantity.toString(),
        `₹${item.cost.toFixed(2)}`,
        `₹${(item.cost * item.quantity).toFixed(2)}`
      ]);

      autoTable(doc, {
        startY: yPosition,
        head: [['Item Name', 'Quantity', 'Unit Price', 'Total']],
        body: separateTableData,
        theme: 'grid',
        styles: { fontSize: 10 },
        headStyles: { fillColor: [255, 153, 0] }
      });
    }
  };

  const generateCombinedPDF = (doc, categories, startY) => {
    let yPosition = startY;

    doc.setFont('helvetica', 'bold');
    doc.setFontSize(14);
    doc.text('Combined Drip & Plumbing Items', 20, yPosition);
    yPosition += 15;

    const allItems = [];
    categories.forEach(category => {
      if (category) {
        const items = selectedItems[category.category_id] || [];
        items.forEach(item => {
          if (!item.printSeparately) {
            allItems.push({
              ...item,
              category: category.category_name
            });
          }
        });
      }
    });

    if (allItems.length > 0) {
      const tableData = allItems.map(item => [
        item.item_name,
        item.category,
        item.quantity.toString(),
        `₹${item.cost.toFixed(2)}`,
        `₹${(item.cost * item.quantity).toFixed(2)}`
      ]);

      autoTable(doc, {
        startY: yPosition,
        head: [['Item Name', 'Category', 'Quantity', 'Unit Price', 'Total']],
        body: tableData,
        theme: 'grid',
        styles: { fontSize: 10 },
        headStyles: { fillColor: [66, 139, 202] }
      });
    }
  };

  const generateCustomPDF = (doc, categoryIds, startY) => {
    let yPosition = startY;

    doc.setFont('helvetica', 'bold');
    doc.setFontSize(14);
    doc.text('Custom Items Report', 20, yPosition);
    yPosition += 15;

    categoryIds.forEach(categoryId => {
      const category = categories.find(cat => cat.category_id === categoryId);
      if (category) {
        generateCategoryPDF(doc, category, yPosition);
        yPosition = doc.lastAutoTable ? doc.lastAutoTable.finalY + 20 : yPosition + 50;
      }
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-xl font-semibold text-white">Print Manager</h3>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-white transition-colors"
        >
          <FaTimes size={20} />
        </button>
      </div>

      {/* Print Options */}
      <div className="bg-gray-800 rounded-lg p-4">
        <h4 className="text-lg font-medium text-white mb-3">Print Options</h4>
        <div className="space-y-2">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={printOptions.includeSeparateItems}
              onChange={(e) => setPrintOptions(prev => ({
                ...prev,
                includeSeparateItems: e.target.checked
              }))}
              className="mr-2"
            />
            <span className="text-gray-300">Include separate items</span>
          </label>
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={printOptions.includeCustomerInfo}
              onChange={(e) => setPrintOptions(prev => ({
                ...prev,
                includeCustomerInfo: e.target.checked
              }))}
              className="mr-2"
            />
            <span className="text-gray-300">Include customer information</span>
          </label>
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={printOptions.includeProjectInfo}
              onChange={(e) => setPrintOptions(prev => ({
                ...prev,
                includeProjectInfo: e.target.checked
              }))}
              className="mr-2"
            />
            <span className="text-gray-300">Include project information</span>
          </label>
        </div>
      </div>

      {/* Quick Print Buttons */}
      <div className="bg-gray-800 rounded-lg p-4">
        <h4 className="text-lg font-medium text-white mb-3">Quick Print</h4>
        <div className="grid grid-cols-2 gap-3">
          <button
            onClick={() => generatePDF('drip')}
            className="flex items-center justify-center px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors"
          >
            <FaPrint className="mr-2" />
            Drip Only
          </button>
          <button
            onClick={() => generatePDF('plumbing')}
            className="flex items-center justify-center px-4 py-2 bg-green-600 hover:bg-green-700 rounded-lg transition-colors"
          >
            <FaPrint className="mr-2" />
            Plumbing Only
          </button>
          <button
            onClick={() => generatePDF('automation')}
            className="flex items-center justify-center px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-lg transition-colors"
          >
            <FaPrint className="mr-2" />
            Automation Only
          </button>
          <button
            onClick={() => generatePDF('drip-plumbing')}
            className="flex items-center justify-center px-4 py-2 bg-orange-600 hover:bg-orange-700 rounded-lg transition-colors"
          >
            <FaPrint className="mr-2" />
            Drip + Plumbing
          </button>
        </div>
      </div>

      {/* Custom Print */}
      <div className="bg-gray-800 rounded-lg p-4">
        <h4 className="text-lg font-medium text-white mb-3">Custom Print</h4>
        <div className="space-y-2 mb-4">
          {categories.map(category => (
            <label key={category.category_id} className="flex items-center">
              <input
                type="checkbox"
                checked={selectedCategories.includes(category.category_id)}
                onChange={() => handleCategoryToggle(category.category_id)}
                className="mr-2"
              />
              <span className="text-gray-300">{category.category_name}</span>
            </label>
          ))}
        </div>
        <button
          onClick={() => generatePDF('custom')}
          disabled={selectedCategories.length === 0}
          className="w-full flex items-center justify-center px-4 py-2 bg-indigo-600 hover:bg-indigo-700 disabled:bg-gray-600 rounded-lg transition-colors"
        >
          <FaDownload className="mr-2" />
          Generate Custom PDF
        </button>
      </div>
    </div>
  );
};

export default PrintManager;
