import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { FaPlus, FaEdit, FaTrash, FaSave, FaTimes } from 'react-icons/fa';

const CategoryManager = ({ categories, onUpdate, showNotification }) => {
  const [editingCategory, setEditingCategory] = useState(null);
  const [newCategory, setNewCategory] = useState('');
  const [showAddForm, setShowAddForm] = useState(false);
  const [loading, setLoading] = useState(false);

  const handleAddCategory = async () => {
    if (!newCategory.trim()) {
      showNotification('Category name is required', 'error');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/rates/categories', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ category_name: newCategory.trim() })
      });

      if (response.ok) {
        showNotification('Category added successfully', 'success');
        setNewCategory('');
        setShowAddForm(false);
        onUpdate();
      } else {
        const error = await response.json();
        throw new Error(error.message || 'Failed to add category');
      }
    } catch (error) {
      console.error('Error adding category:', error);
      showNotification(error.message, 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleEditCategory = async (categoryId, newName) => {
    if (!newName.trim()) {
      showNotification('Category name is required', 'error');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/rates/categories', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          category_id: categoryId, 
          category_name: newName.trim() 
        })
      });

      if (response.ok) {
        showNotification('Category updated successfully', 'success');
        setEditingCategory(null);
        onUpdate();
      } else {
        const error = await response.json();
        throw new Error(error.message || 'Failed to update category');
      }
    } catch (error) {
      console.error('Error updating category:', error);
      showNotification(error.message, 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteCategory = async (categoryId, categoryName) => {
    if (!confirm(`Are you sure you want to delete the category "${categoryName}"? This will also delete all associated items.`)) {
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/rates/categories', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ category_id: categoryId })
      });

      if (response.ok) {
        showNotification('Category deleted successfully', 'success');
        onUpdate();
      } else {
        const error = await response.json();
        throw new Error(error.message || 'Failed to delete category');
      }
    } catch (error) {
      console.error('Error deleting category:', error);
      showNotification(error.message, 'error');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Add Category Section */}
      <div className="bg-gray-900 rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-white">Category Management</h2>
          <button
            onClick={() => setShowAddForm(!showAddForm)}
            className="flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 rounded-lg transition-colors"
            disabled={loading}
          >
            <FaPlus className="mr-2" />
            Add Category
          </button>
        </div>

        {showAddForm && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="border-t border-gray-700 pt-4"
          >
            <div className="flex gap-4 items-center">
              <input
                type="text"
                placeholder="Enter category name..."
                value={newCategory}
                onChange={(e) => setNewCategory(e.target.value)}
                className="flex-1 px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-blue-500"
                onKeyPress={(e) => e.key === 'Enter' && handleAddCategory()}
              />
              <button
                onClick={handleAddCategory}
                disabled={loading || !newCategory.trim()}
                className="flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 rounded-lg transition-colors"
              >
                <FaSave className="mr-2" />
                Save
              </button>
              <button
                onClick={() => {
                  setShowAddForm(false);
                  setNewCategory('');
                }}
                className="flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded-lg transition-colors"
              >
                <FaTimes className="mr-2" />
                Cancel
              </button>
            </div>
          </motion.div>
        )}
      </div>

      {/* Categories List */}
      <div className="bg-gray-900 rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-800">
              <tr>
                <th className="px-6 py-4 text-left text-sm font-medium text-gray-300 uppercase tracking-wider">
                  Category ID
                </th>
                <th className="px-6 py-4 text-left text-sm font-medium text-gray-300 uppercase tracking-wider">
                  Category Name
                </th>
                <th className="px-6 py-4 text-left text-sm font-medium text-gray-300 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-700">
              {categories.length === 0 ? (
                <tr>
                  <td colSpan="3" className="px-6 py-8 text-center text-gray-400">
                    No categories found. Add a category to get started.
                  </td>
                </tr>
              ) : (
                categories.map((category) => (
                  <tr key={category.category_id} className="hover:bg-gray-800 transition-colors">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                      {category.category_id}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-white">
                      {editingCategory === category.category_id ? (
                        <input
                          type="text"
                          defaultValue={category.category_name}
                          className="px-3 py-1 bg-gray-800 border border-gray-700 rounded text-white focus:outline-none focus:border-blue-500"
                          onKeyPress={(e) => {
                            if (e.key === 'Enter') {
                              handleEditCategory(category.category_id, e.target.value);
                            }
                          }}
                          onBlur={(e) => handleEditCategory(category.category_id, e.target.value)}
                          autoFocus
                        />
                      ) : (
                        category.category_name
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                      <div className="flex space-x-2">
                        {editingCategory === category.category_id ? (
                          <button
                            onClick={() => setEditingCategory(null)}
                            className="text-gray-400 hover:text-gray-300 transition-colors"
                            title="Cancel"
                          >
                            <FaTimes />
                          </button>
                        ) : (
                          <>
                            <button
                              onClick={() => setEditingCategory(category.category_id)}
                              className="text-blue-400 hover:text-blue-300 transition-colors"
                              title="Edit"
                              disabled={loading}
                            >
                              <FaEdit />
                            </button>
                            <button
                              onClick={() => handleDeleteCategory(category.category_id, category.category_name)}
                              className="text-red-400 hover:text-red-300 transition-colors"
                              title="Delete"
                              disabled={loading}
                            >
                              <FaTrash />
                            </button>
                          </>
                        )}
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {loading && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 flex items-center space-x-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <span className="text-white">Processing...</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default CategoryManager;
