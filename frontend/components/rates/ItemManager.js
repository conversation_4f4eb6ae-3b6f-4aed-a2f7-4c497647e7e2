import React, { useState, useEffect } from 'react';
import { FaSave, FaTimes } from 'react-icons/fa';

const ItemManager = ({ item, categories, onSave, onCancel, showNotification, isEdit }) => {
  const [formData, setFormData] = useState({
    item_name: '',
    category_id: '',
    quantity: 1,
    price_pu: ''
  });
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (isEdit && item) {
      setFormData({
        item_name: item.item_name || '',
        category_id: item.category_id || '',
        quantity: item.quantity || 1,
        price_pu: item.price_pu || ''
      });
    }
  }, [item, isEdit]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const validateForm = () => {
    if (!formData.item_name.trim()) {
      showNotification('Item name is required', 'error');
      return false;
    }
    if (!formData.category_id) {
      showNotification('Category is required', 'error');
      return false;
    }
    if (!formData.price_pu || parseFloat(formData.price_pu) <= 0) {
      showNotification('Valid price is required', 'error');
      return false;
    }
    if (!formData.quantity || parseInt(formData.quantity) <= 0) {
      showNotification('Valid quantity is required', 'error');
      return false;
    }
    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setLoading(true);
    try {
      const url = isEdit ? '/api/rates/updateRate' : '/api/rates/addRate';
      const method = isEdit ? 'PUT' : 'POST';
      
      const payload = {
        ...formData,
        quantity: parseInt(formData.quantity),
        price_pu: parseFloat(formData.price_pu)
      };

      if (isEdit) {
        payload.rate_id = item.rate_id;
      } else {
        // For new items, we need to create a stock entry first if it doesn't exist
        payload.item_id = null; // Will be handled by the API
      }

      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      });

      if (response.ok) {
        showNotification(
          isEdit ? 'Item updated successfully' : 'Item added successfully',
          'success'
        );
        onSave();
      } else {
        const error = await response.json();
        throw new Error(error.message || 'Failed to save item');
      }
    } catch (error) {
      console.error('Error saving item:', error);
      showNotification(error.message, 'error');
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Item Name *
        </label>
        <input
          type="text"
          name="item_name"
          value={formData.item_name}
          onChange={handleChange}
          className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-blue-500"
          placeholder="Enter item name"
          required
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Category *
        </label>
        <select
          name="category_id"
          value={formData.category_id}
          onChange={handleChange}
          className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-blue-500"
          required
        >
          <option value="">Select a category</option>
          {categories.map(category => (
            <option key={category.category_id} value={category.category_id}>
              {category.category_name}
            </option>
          ))}
        </select>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Quantity *
          </label>
          <input
            type="number"
            name="quantity"
            value={formData.quantity}
            onChange={handleChange}
            min="1"
            className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-blue-500"
            placeholder="Enter quantity"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Price per Unit (₹) *
          </label>
          <input
            type="number"
            name="price_pu"
            value={formData.price_pu}
            onChange={handleChange}
            min="0"
            step="0.01"
            className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-blue-500"
            placeholder="Enter price"
            required
          />
        </div>
      </div>

      <div className="flex justify-end space-x-4 pt-4 border-t border-gray-700">
        <button
          type="button"
          onClick={onCancel}
          className="flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded-lg transition-colors"
          disabled={loading}
        >
          <FaTimes className="mr-2" />
          Cancel
        </button>
        <button
          type="submit"
          className="flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 rounded-lg transition-colors"
          disabled={loading}
        >
          <FaSave className="mr-2" />
          {loading ? 'Saving...' : (isEdit ? 'Update' : 'Save')}
        </button>
      </div>
    </form>
  );
};

export default ItemManager;
