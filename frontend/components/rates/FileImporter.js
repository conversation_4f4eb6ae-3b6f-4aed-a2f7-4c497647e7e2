import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { FaUpload, FaDownload, FaFileExcel, FaFileCsv, FaCheck, FaTimes } from 'react-icons/fa';

const FileImporter = ({ categories, onUpdate, showNotification }) => {
  const [file, setFile] = useState(null);
  const [preview, setPreview] = useState([]);
  const [loading, setLoading] = useState(false);
  const [importResults, setImportResults] = useState(null);

  const handleFileSelect = (e) => {
    const selectedFile = e.target.files[0];
    if (!selectedFile) return;

    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
      'application/vnd.ms-excel', // .xls
      'text/csv' // .csv
    ];

    if (!allowedTypes.includes(selectedFile.type)) {
      showNotification('Please select a valid Excel (.xlsx, .xls) or CSV file', 'error');
      return;
    }

    setFile(selectedFile);
    processFilePreview(selectedFile);
  };

  const processFilePreview = async (file) => {
    setLoading(true);
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('preview', 'true');

      const response = await fetch('/api/rates/import', {
        method: 'POST',
        body: formData
      });

      const data = await response.json();
      
      if (data.success) {
        setPreview(data.preview || []);
      } else {
        throw new Error(data.message || 'Failed to process file');
      }
    } catch (error) {
      console.error('Error processing file:', error);
      showNotification(error.message, 'error');
      setFile(null);
    } finally {
      setLoading(false);
    }
  };

  const handleImport = async () => {
    if (!file) {
      showNotification('Please select a file first', 'error');
      return;
    }

    setLoading(true);
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch('/api/rates/import', {
        method: 'POST',
        body: formData
      });

      const data = await response.json();
      
      if (data.success) {
        setImportResults(data);
        showNotification(
          `Import completed! ${data.imported} items imported, ${data.errors?.length || 0} errors`,
          'success'
        );
        onUpdate();
      } else {
        throw new Error(data.message || 'Import failed');
      }
    } catch (error) {
      console.error('Error importing file:', error);
      showNotification(error.message, 'error');
    } finally {
      setLoading(false);
    }
  };

  const downloadTemplate = () => {
    const csvContent = [
      'item_name,category_name,quantity,price_pu',
      'Sample Item 1,Tools,10,500.00',
      'Sample Item 2,Electronics,5,1500.00',
      'Sample Item 3,Plumbing,20,250.00'
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'rates_import_template.csv';
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
    showNotification('Template downloaded successfully', 'success');
  };

  const resetImport = () => {
    setFile(null);
    setPreview([]);
    setImportResults(null);
  };

  return (
    <div className="space-y-6">
      {/* Instructions */}
      <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-blue-400 mb-3">Import Instructions</h3>
        <ul className="text-sm text-gray-300 space-y-2">
          <li>• Supported formats: Excel (.xlsx, .xls) and CSV (.csv)</li>
          <li>• Required columns: item_name, category_name, quantity, price_pu</li>
          <li>• Category names must match existing categories</li>
          <li>• Quantity and price must be positive numbers</li>
          <li>• Duplicate items will be updated with new values</li>
        </ul>
        <button
          onClick={downloadTemplate}
          className="mt-4 flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors"
        >
          <FaDownload className="mr-2" />
          Download Template
        </button>
      </div>

      {/* File Upload */}
      <div className="bg-gray-900 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Upload File</h3>
        
        <div className="border-2 border-dashed border-gray-600 rounded-lg p-8 text-center">
          <input
            type="file"
            accept=".xlsx,.xls,.csv"
            onChange={handleFileSelect}
            className="hidden"
            id="file-upload"
          />
          <label
            htmlFor="file-upload"
            className="cursor-pointer flex flex-col items-center space-y-4"
          >
            <div className="text-4xl text-gray-400">
              {file ? (
                file.name.endsWith('.csv') ? <FaFileCsv className="text-green-500" /> : <FaFileExcel className="text-green-500" />
              ) : (
                <FaUpload />
              )}
            </div>
            <div>
              <p className="text-lg text-white">
                {file ? file.name : 'Click to select file or drag and drop'}
              </p>
              <p className="text-sm text-gray-400">
                Supports Excel (.xlsx, .xls) and CSV files
              </p>
            </div>
          </label>
        </div>

        {file && (
          <div className="mt-4 flex justify-between items-center">
            <span className="text-sm text-gray-400">
              File selected: {file.name} ({(file.size / 1024).toFixed(1)} KB)
            </span>
            <div className="space-x-2">
              <button
                onClick={resetImport}
                className="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded-lg transition-colors"
              >
                Clear
              </button>
              <button
                onClick={handleImport}
                disabled={loading || !file}
                className="px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 rounded-lg transition-colors"
              >
                {loading ? 'Importing...' : 'Import Data'}
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Preview */}
      {preview.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-900 rounded-lg overflow-hidden"
        >
          <div className="px-6 py-4 bg-gray-800 border-b border-gray-700">
            <h3 className="text-lg font-semibold text-white">Preview ({preview.length} rows)</h3>
          </div>
          <div className="overflow-x-auto max-h-96">
            <table className="w-full">
              <thead className="bg-gray-800 sticky top-0">
                <tr>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-300">Item Name</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-300">Category</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-300">Quantity</th>
                  <th className="px-4 py-3 text-left text-sm font-medium text-gray-300">Price</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-700">
                {preview.slice(0, 10).map((row, index) => (
                  <tr key={index} className="hover:bg-gray-800">
                    <td className="px-4 py-3 text-sm text-white">{row.item_name}</td>
                    <td className="px-4 py-3 text-sm text-gray-300">{row.category_name}</td>
                    <td className="px-4 py-3 text-sm text-gray-300">{row.quantity}</td>
                    <td className="px-4 py-3 text-sm text-green-400">₹{row.price_pu}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          {preview.length > 10 && (
            <div className="px-6 py-3 bg-gray-800 text-sm text-gray-400 text-center">
              Showing first 10 rows of {preview.length} total rows
            </div>
          )}
        </motion.div>
      )}

      {/* Import Results */}
      {importResults && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gray-900 rounded-lg p-6"
        >
          <h3 className="text-lg font-semibold text-white mb-4">Import Results</h3>
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div className="bg-green-900/30 border border-green-500/30 rounded-lg p-4">
              <div className="flex items-center">
                <FaCheck className="text-green-400 mr-2" />
                <span className="text-green-400 font-medium">Successfully Imported</span>
              </div>
              <p className="text-2xl font-bold text-white mt-2">{importResults.imported || 0}</p>
            </div>
            <div className="bg-red-900/30 border border-red-500/30 rounded-lg p-4">
              <div className="flex items-center">
                <FaTimes className="text-red-400 mr-2" />
                <span className="text-red-400 font-medium">Errors</span>
              </div>
              <p className="text-2xl font-bold text-white mt-2">{importResults.errors?.length || 0}</p>
            </div>
          </div>
          
          {importResults.errors && importResults.errors.length > 0 && (
            <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-4">
              <h4 className="text-red-400 font-medium mb-2">Errors:</h4>
              <ul className="text-sm text-gray-300 space-y-1 max-h-32 overflow-y-auto">
                {importResults.errors.map((error, index) => (
                  <li key={index}>• {error}</li>
                ))}
              </ul>
            </div>
          )}
        </motion.div>
      )}

      {loading && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 flex items-center space-x-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <span className="text-white">Processing file...</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default FileImporter;
