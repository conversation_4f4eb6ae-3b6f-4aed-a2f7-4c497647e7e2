import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FaPrint, FaDownload, FaSave, FaEdit } from 'react-icons/fa';
import BackButton from '@/components/BackButton';
import ScrollToTopButton from '@/components/scrollup';
import Modal from '@/components/Modal';
import PrintManager from '@/components/quotes/PrintManager';

const EnhancedQuoteManager = () => {
  const [projectId, setProjectId] = useState('');
  const [projectData, setProjectData] = useState(null);
  const [customerData, setCustomerData] = useState(null);
  const [editableAddress, setEditableAddress] = useState('');
  const [categories, setCategories] = useState([]);
  const [selectedItems, setSelectedItems] = useState({});
  const [itemsByCategory, setItemsByCategory] = useState({});
  const [labourCost, setLabourCost] = useState(0);
  const [miscellaneousItems, setMiscellaneousItems] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showPrintModal, setShowPrintModal] = useState(false);
  const [notification, setNotification] = useState({ show: false, message: '', type: '' });

  // Fetch project and customer data
  const fetchProjectCustomerData = async (pid) => {
    if (!pid) return;

    setLoading(true);
    try {
      const response = await fetch(`/api/quotes/customer-project?project_id=${pid}`);
      const data = await response.json();

      if (response.ok) {
        setProjectData(data.project);
        setCustomerData(data.customer);
        setEditableAddress(data.customer.address || '');
      } else {
        throw new Error(data.error || 'Failed to fetch project data');
      }
    } catch (error) {
      console.error('Error fetching project data:', error);
      showNotification(error.message, 'error');
    } finally {
      setLoading(false);
    }
  };

  // Fetch categories and items
  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/categories');
      const data = await response.json();
      if (data.success) {
        setCategories(data.categories);
        
        // Fetch items for each category
        const itemsData = {};
        for (const category of data.categories) {
          const itemsResponse = await fetch(`/api/items/${category.category_id}`);
          const items = await itemsResponse.json();
          itemsData[category.category_id] = items;
        }
        setItemsByCategory(itemsData);
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
      showNotification('Failed to load categories', 'error');
    }
  };

  useEffect(() => {
    fetchCategories();
  }, []);

  useEffect(() => {
    if (projectId) {
      fetchProjectCustomerData(projectId);
    }
  }, [projectId]);

  const showNotification = (message, type) => {
    setNotification({ show: true, message, type });
    setTimeout(() => setNotification({ show: false, message: '', type: '' }), 3000);
  };

  const handleItemSelection = (categoryId, item, quantity = 1) => {
    setSelectedItems(prev => ({
      ...prev,
      [categoryId]: [
        ...(prev[categoryId] || []),
        {
          ...item,
          quantity,
          cost: item.price_pu,
          printSeparately: false
        }
      ]
    }));
  };

  const handleItemRemoval = (categoryId, itemIndex) => {
    setSelectedItems(prev => ({
      ...prev,
      [categoryId]: prev[categoryId].filter((_, index) => index !== itemIndex)
    }));
  };

  const togglePrintSeparately = (categoryId, itemIndex) => {
    setSelectedItems(prev => ({
      ...prev,
      [categoryId]: prev[categoryId].map((item, index) => 
        index === itemIndex 
          ? { ...item, printSeparately: !item.printSeparately }
          : item
      )
    }));
  };

  const calculateCategoryTotal = (categoryId) => {
    const items = selectedItems[categoryId] || [];
    return items.reduce((sum, item) => {
      if (!item.printSeparately) {
        return sum + (item.cost * item.quantity);
      }
      return sum;
    }, 0);
  };

  const calculateSeparateItemsTotal = () => {
    return categories.reduce((sum, category) => {
      const items = selectedItems[category.category_id] || [];
      return sum + items.reduce((catSum, item) => {
        if (item.printSeparately) {
          return catSum + (item.cost * item.quantity);
        }
        return catSum;
      }, 0);
    }, 0);
  };

  const calculateMiscellaneousTotal = () => {
    return miscellaneousItems.reduce((sum, item) => sum + (item.cost * item.quantity), 0);
  };

  const calculateGrandTotal = () => {
    const categoryTotals = categories.reduce((sum, cat) => sum + calculateCategoryTotal(cat.category_id), 0);
    const separateTotal = calculateSeparateItemsTotal();
    const miscTotal = calculateMiscellaneousTotal();
    const labour = parseFloat(labourCost) || 0;
    
    return categoryTotals + separateTotal + miscTotal + labour;
  };

  const saveQuote = async () => {
    if (!projectData || !customerData) {
      showNotification('Please select a project first', 'error');
      return;
    }

    setLoading(true);
    try {
      const categoryCosts = {};
      categories.forEach(cat => {
        categoryCosts[`${cat.category_name.toLowerCase()}_cost`] = calculateCategoryTotal(cat.category_id);
      });

      const response = await fetch('/api/save-quote', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          project_id: projectData.id,
          customer_name: customerData.name,
          ...categoryCosts,
          labour_cost: parseFloat(labourCost) || 0,
          additional_cost: calculateMiscellaneousTotal() + calculateSeparateItemsTotal(),
          total_cost: calculateGrandTotal(),
        }),
      });

      if (response.ok) {
        showNotification('Quote saved successfully!', 'success');
      } else {
        throw new Error('Failed to save quote');
      }
    } catch (error) {
      console.error('Error saving quote:', error);
      showNotification('Failed to save quote', 'error');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-black text-white">
      <div className="absolute top-4 left-4 z-10">
        <BackButton route="/quotation/home" />
      </div>
      <ScrollToTopButton />

      {/* Notification */}
      {notification.show && (
        <motion.div
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -50 }}
          className={`fixed top-4 right-4 z-50 px-6 py-3 rounded-lg shadow-lg ${
            notification.type === 'success' ? 'bg-green-600' : 'bg-red-600'
          }`}
        >
          {notification.message}
        </motion.div>
      )}

      <div className="container mx-auto px-4 py-20">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <h1 className="text-4xl font-bold mb-4">Enhanced Quote Manager</h1>
          <p className="text-gray-400">Generate professional quotes with CRM integration</p>
        </motion.div>

        {/* Project Selection */}
        <div className="bg-gray-900 rounded-lg p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">Project Selection</h2>
          <div className="flex gap-4 items-center">
            <input
              type="number"
              placeholder="Enter Project ID"
              value={projectId}
              onChange={(e) => setProjectId(e.target.value)}
              className="px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-blue-500"
            />
            <button
              onClick={() => fetchProjectCustomerData(projectId)}
              disabled={!projectId || loading}
              className="px-6 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 rounded-lg transition-colors"
            >
              {loading ? 'Loading...' : 'Load Project'}
            </button>
          </div>
        </div>

        {/* Customer Information */}
        {customerData && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-gray-900 rounded-lg p-6 mb-8"
          >
            <h2 className="text-xl font-semibold mb-4">Customer Information</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Customer Name (Read-only)
                </label>
                <input
                  type="text"
                  value={customerData.name}
                  readOnly
                  className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-gray-300 cursor-not-allowed"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Phone Number (Read-only)
                </label>
                <input
                  type="text"
                  value={customerData.phone}
                  readOnly
                  className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-gray-300 cursor-not-allowed"
                />
              </div>
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Address (Editable)
                </label>
                <textarea
                  value={editableAddress}
                  onChange={(e) => setEditableAddress(e.target.value)}
                  placeholder="Enter customer address..."
                  rows={3}
                  className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-blue-500"
                />
              </div>
            </div>
          </motion.div>
        )}

        {/* Project Information */}
        {projectData && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-gray-900 rounded-lg p-6 mb-8"
          >
            <h2 className="text-xl font-semibold mb-4">Project Information</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <span className="text-gray-400">Project Name:</span>
                <p className="text-white font-medium">{projectData.name}</p>
              </div>
              <div>
                <span className="text-gray-400">Start Date:</span>
                <p className="text-white font-medium">{projectData.start_date}</p>
              </div>
              <div>
                <span className="text-gray-400">Status:</span>
                <p className="text-white font-medium">{projectData.status}</p>
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default EnhancedQuoteManager;
