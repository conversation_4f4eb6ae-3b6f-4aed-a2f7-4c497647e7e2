import { connectToDB } from "@/lib/db";

export default async function handler(req, res) {
  if (req.method !== "GET") {
    return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
  }

  const { project_id } = req.query;

  if (!project_id) {
    return res.status(400).json({ error: "Project ID is required" });
  }

  let db;
  try {
    db = await connectToDB();

    // Fetch project with customer information
    const [result] = await db.execute(`
      SELECT 
        p.pid,
        p.pname,
        p.start_date,
        p.end_date,
        p.status as project_status,
        c.cid,
        c.cname as customer_name,
        c.cphone,
        c.alternate_phone,
        c.status as customer_status,
        c.remark
      FROM project p
      LEFT JOIN customer c ON p.cid = c.cid
      WHERE p.pid = ?
    `, [project_id]);

    if (result.length === 0) {
      return res.status(404).json({ error: "Project not found" });
    }

    const projectData = result[0];

    // Format the response
    const response = {
      project: {
        id: projectData.pid,
        name: projectData.pname,
        start_date: projectData.start_date,
        end_date: projectData.end_date,
        status: projectData.project_status
      },
      customer: {
        id: projectData.cid,
        name: projectData.customer_name,
        phone: projectData.cphone,
        alternate_phone: projectData.alternate_phone,
        status: projectData.customer_status,
        remark: projectData.remark,
        // Default address field that can be edited
        address: ''
      }
    };

    res.status(200).json(response);
  } catch (error) {
    console.error("Database error:", error);
    res.status(500).json({ error: "Internal Server Error", details: error.message });
  } finally {
    if (db) db.release();
  }
}
