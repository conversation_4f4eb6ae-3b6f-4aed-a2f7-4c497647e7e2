import { connectToDB } from "@/lib/db";

export default async function handler(req, res) {
  if (req.method !== "DELETE") {
    return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
  }

  const { rate_id } = req.body;

  if (!rate_id) {
    return res.status(400).json({ error: "Rate ID is required" });
  }

  let db;
  try {
    db = await connectToDB();

    // Check if rate exists
    const [existing] = await db.execute(
      "SELECT rate_id, item_name FROM rates WHERE rate_id = ?",
      [rate_id]
    );

    if (existing.length === 0) {
      return res.status(404).json({ error: "Rate not found" });
    }

    // Delete the rate
    await db.execute(
      "DELETE FROM rates WHERE rate_id = ?",
      [rate_id]
    );

    res.status(200).json({ 
      success: true, 
      message: "Rate deleted successfully" 
    });
  } catch (error) {
    console.error("Database error:", error);
    res.status(500).json({ error: "Internal Server Error", details: error.message });
  } finally {
    if (db) db.release();
  }
}
