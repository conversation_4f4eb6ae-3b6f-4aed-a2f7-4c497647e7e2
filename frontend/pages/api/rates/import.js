import { connectToDB } from "@/lib/db";
import formidable from 'formidable';
import fs from 'fs';
import csv from 'csv-parser';
import * as XLSX from 'xlsx';

export const config = {
  api: {
    bodyParser: false,
  },
};

export default async function handler(req, res) {
  if (req.method !== "POST") {
    return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
  }

  let db;
  try {
    db = await connectToDB();

    const form = formidable({
      uploadDir: '/tmp',
      keepExtensions: true,
      maxFileSize: 10 * 1024 * 1024, // 10MB
    });

    const [fields, files] = await form.parse(req);
    const file = files.file?.[0];
    const isPreview = fields.preview?.[0] === 'true';

    if (!file) {
      return res.status(400).json({ error: "No file uploaded" });
    }

    // Parse file based on type
    let data = [];
    const filePath = file.filepath;
    const fileName = file.originalFilename || '';

    try {
      if (fileName.endsWith('.csv')) {
        data = await parseCSV(filePath);
      } else if (fileName.endsWith('.xlsx') || fileName.endsWith('.xls')) {
        data = await parseExcel(filePath);
      } else {
        throw new Error('Unsupported file format');
      }

      // Clean up temp file
      fs.unlinkSync(filePath);

      if (isPreview) {
        return res.status(200).json({
          success: true,
          preview: data.slice(0, 50), // Return first 50 rows for preview
          total: data.length
        });
      }

      // Process import
      const result = await processImport(db, data);
      return res.status(200).json(result);

    } catch (parseError) {
      // Clean up temp file on error
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
      throw parseError;
    }

  } catch (error) {
    console.error("Import error:", error);
    return res.status(500).json({ 
      error: "Import failed", 
      message: error.message 
    });
  } finally {
    if (db) db.release();
  }
}

async function parseCSV(filePath) {
  return new Promise((resolve, reject) => {
    const results = [];
    fs.createReadStream(filePath)
      .pipe(csv())
      .on('data', (data) => results.push(data))
      .on('end', () => resolve(results))
      .on('error', reject);
  });
}

async function parseExcel(filePath) {
  const workbook = XLSX.readFile(filePath);
  const sheetName = workbook.SheetNames[0];
  const worksheet = workbook.Sheets[sheetName];
  return XLSX.utils.sheet_to_json(worksheet);
}

async function processImport(db, data) {
  const errors = [];
  const imported = [];
  let importedCount = 0;

  // Get existing categories
  const [categories] = await db.execute("SELECT category_id, category_name FROM category");
  const categoryMap = new Map();
  categories.forEach(cat => {
    categoryMap.set(cat.category_name.toLowerCase(), cat.category_id);
  });

  // Process each row
  for (let i = 0; i < data.length; i++) {
    const row = data[i];
    const rowNum = i + 1;

    try {
      // Validate required fields
      const itemName = row.item_name?.toString().trim();
      const categoryName = row.category_name?.toString().trim();
      const quantity = parseInt(row.quantity);
      const pricePerUnit = parseFloat(row.price_pu);

      if (!itemName) {
        errors.push(`Row ${rowNum}: Item name is required`);
        continue;
      }

      if (!categoryName) {
        errors.push(`Row ${rowNum}: Category name is required`);
        continue;
      }

      if (!categoryMap.has(categoryName.toLowerCase())) {
        errors.push(`Row ${rowNum}: Category "${categoryName}" not found`);
        continue;
      }

      if (isNaN(quantity) || quantity <= 0) {
        errors.push(`Row ${rowNum}: Invalid quantity "${row.quantity}"`);
        continue;
      }

      if (isNaN(pricePerUnit) || pricePerUnit < 0) {
        errors.push(`Row ${rowNum}: Invalid price "${row.price_pu}"`);
        continue;
      }

      const categoryId = categoryMap.get(categoryName.toLowerCase());

      // Check if item already exists in rates
      const [existingRate] = await db.execute(
        "SELECT rate_id FROM rates WHERE item_name = ? AND category_id = ?",
        [itemName, categoryId]
      );

      if (existingRate.length > 0) {
        // Update existing rate
        await db.execute(
          "UPDATE rates SET quantity = ?, price_pu = ? WHERE rate_id = ?",
          [quantity, pricePerUnit, existingRate[0].rate_id]
        );
      } else {
        // Check if item exists in stock, if not create it
        const [existingStock] = await db.execute(
          "SELECT stock_id FROM stock WHERE item_name = ? AND category_id = ?",
          [itemName, categoryId]
        );

        let itemId;
        if (existingStock.length > 0) {
          itemId = existingStock[0].stock_id;
        } else {
          // Create new stock item
          const [stockResult] = await db.execute(
            "INSERT INTO stock (item_name, category_id, quantity, price_pu) VALUES (?, ?, ?, ?)",
            [itemName, categoryId, quantity, pricePerUnit]
          );
          itemId = stockResult.insertId;
        }

        // Create new rate
        await db.execute(
          "INSERT INTO rates (item_id, item_name, quantity, price_pu, category_id) VALUES (?, ?, ?, ?, ?)",
          [itemId, itemName, quantity, pricePerUnit, categoryId]
        );
      }

      imported.push({ itemName, categoryName, quantity, pricePerUnit });
      importedCount++;

    } catch (error) {
      console.error(`Error processing row ${rowNum}:`, error);
      errors.push(`Row ${rowNum}: ${error.message}`);
    }
  }

  return {
    success: true,
    imported: importedCount,
    errors: errors,
    total: data.length
  };
}
