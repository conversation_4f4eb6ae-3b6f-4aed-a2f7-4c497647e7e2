import { connectToDB } from "@/lib/db";

export default async function handler(req, res) {
  let db;
  
  try {
    db = await connectToDB();

    switch (req.method) {
      case 'GET':
        return await handleGet(db, res);
      case 'POST':
        return await handlePost(db, req, res);
      case 'PUT':
        return await handlePut(db, req, res);
      case 'DELETE':
        return await handleDelete(db, req, res);
      default:
        return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error("Database error:", error);
    return res.status(500).json({ error: "Internal Server Error", details: error.message });
  } finally {
    if (db) db.release();
  }
}

async function handleGet(db, res) {
  try {
    const [categories] = await db.execute(
      "SELECT category_id, category_name FROM category ORDER BY category_name"
    );
    
    return res.status(200).json({ 
      success: true, 
      categories: categories || [] 
    });
  } catch (error) {
    console.error("Error fetching categories:", error);
    return res.status(500).json({ error: "Failed to fetch categories" });
  }
}

async function handlePost(db, req, res) {
  const { category_name } = req.body;

  if (!category_name || !category_name.trim()) {
    return res.status(400).json({ error: "Category name is required" });
  }

  try {
    // Check if category already exists
    const [existing] = await db.execute(
      "SELECT category_id FROM category WHERE category_name = ?",
      [category_name.trim()]
    );

    if (existing.length > 0) {
      return res.status(400).json({ error: "Category already exists" });
    }

    // Get the next available category_id
    const [maxId] = await db.execute(
      "SELECT MAX(category_id) as max_id FROM category"
    );
    const nextId = (maxId[0]?.max_id || 0) + 1;

    // Insert new category
    await db.execute(
      "INSERT INTO category (category_id, category_name) VALUES (?, ?)",
      [nextId, category_name.trim()]
    );

    return res.status(201).json({ 
      success: true, 
      message: "Category added successfully",
      category_id: nextId
    });
  } catch (error) {
    console.error("Error adding category:", error);
    if (error.code === 'ER_DUP_ENTRY') {
      return res.status(400).json({ error: "Category already exists" });
    }
    return res.status(500).json({ error: "Failed to add category" });
  }
}

async function handlePut(db, req, res) {
  const { category_id, category_name } = req.body;

  if (!category_id || !category_name || !category_name.trim()) {
    return res.status(400).json({ error: "Category ID and name are required" });
  }

  try {
    // Check if category exists
    const [existing] = await db.execute(
      "SELECT category_id FROM category WHERE category_id = ?",
      [category_id]
    );

    if (existing.length === 0) {
      return res.status(404).json({ error: "Category not found" });
    }

    // Check if new name conflicts with existing category
    const [nameConflict] = await db.execute(
      "SELECT category_id FROM category WHERE category_name = ? AND category_id != ?",
      [category_name.trim(), category_id]
    );

    if (nameConflict.length > 0) {
      return res.status(400).json({ error: "Category name already exists" });
    }

    // Update category
    await db.execute(
      "UPDATE category SET category_name = ? WHERE category_id = ?",
      [category_name.trim(), category_id]
    );

    return res.status(200).json({ 
      success: true, 
      message: "Category updated successfully" 
    });
  } catch (error) {
    console.error("Error updating category:", error);
    return res.status(500).json({ error: "Failed to update category" });
  }
}

async function handleDelete(db, req, res) {
  const { category_id } = req.body;

  if (!category_id) {
    return res.status(400).json({ error: "Category ID is required" });
  }

  try {
    // Check if category exists
    const [existing] = await db.execute(
      "SELECT category_id FROM category WHERE category_id = ?",
      [category_id]
    );

    if (existing.length === 0) {
      return res.status(404).json({ error: "Category not found" });
    }

    // Check if category has associated rates
    const [rates] = await db.execute(
      "SELECT COUNT(*) as count FROM rates WHERE category_id = ?",
      [category_id]
    );

    if (rates[0].count > 0) {
      return res.status(400).json({ 
        error: `Cannot delete category. It has ${rates[0].count} associated items. Please delete the items first.` 
      });
    }

    // Check if category has associated stock items
    const [stock] = await db.execute(
      "SELECT COUNT(*) as count FROM stock WHERE category_id = ?",
      [category_id]
    );

    if (stock[0].count > 0) {
      return res.status(400).json({ 
        error: `Cannot delete category. It has ${stock[0].count} associated stock items. Please delete the stock items first.` 
      });
    }

    // Delete category
    await db.execute(
      "DELETE FROM category WHERE category_id = ?",
      [category_id]
    );

    return res.status(200).json({ 
      success: true, 
      message: "Category deleted successfully" 
    });
  } catch (error) {
    console.error("Error deleting category:", error);
    return res.status(500).json({ error: "Failed to delete category" });
  }
}
