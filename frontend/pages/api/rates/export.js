import { connectToDB } from "@/lib/db";

export default async function handler(req, res) {
  if (req.method !== "GET") {
    return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
  }

  let db;
  try {
    db = await connectToDB();

    // Fetch all rates with category information
    const [rates] = await db.execute(`
      SELECT 
        r.rate_id,
        r.item_name,
        c.category_name,
        r.quantity,
        r.price_pu
      FROM rates r
      LEFT JOIN category c ON r.category_id = c.category_id
      ORDER BY c.category_name, r.item_name
    `);

    // Generate CSV content
    const csvHeaders = ['Rate ID', 'Item Name', 'Category', 'Quantity', 'Price per Unit'];
    const csvRows = rates.map(rate => [
      rate.rate_id,
      `"${rate.item_name}"`, // Wrap in quotes to handle commas in names
      `"${rate.category_name || 'Unknown'}"`,
      rate.quantity,
      parseFloat(rate.price_pu).toFixed(2)
    ]);

    const csvContent = [
      csvHeaders.join(','),
      ...csvRows.map(row => row.join(','))
    ].join('\n');

    // Set headers for file download
    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', `attachment; filename="rates_export_${new Date().toISOString().split('T')[0]}.csv"`);
    
    res.status(200).send(csvContent);
  } catch (error) {
    console.error("Database error:", error);
    res.status(500).json({ error: "Internal Server Error", details: error.message });
  } finally {
    if (db) db.release();
  }
}
