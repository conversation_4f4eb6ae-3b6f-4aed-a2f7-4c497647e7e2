import { connectToDB } from "../../../lib/db";

export default async function handler(req, res) {
  if (req.method !== "POST") {
    return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
  }

  const { item_id, price_pu, item_name, category_id, quantity } = req.body;

  let db;
  try {
    db = await connectToDB();

    if (item_id) {
      // Legacy mode: adding rate from existing stock item
      if (price_pu === undefined) {
        return res.status(400).json({ error: "Item ID and price are required" });
      }

      await db.execute(
        `INSERT INTO rates (item_id, item_name, quantity, price_pu, category_id)
         SELECT ?, item_name, quantity, ?, category_id FROM stock WHERE stock_id = ?`,
        [
          item_id,
          parseFloat(price_pu) || 0,
          item_id
        ]
      );
    } else {
      // New mode: creating new item and rate
      if (!item_name || !category_id || !quantity || price_pu === undefined) {
        return res.status(400).json({ error: "Item name, category, quantity, and price are required" });
      }

      // Check if item already exists in stock
      const [existingStock] = await db.execute(
        "SELECT stock_id FROM stock WHERE item_name = ? AND category_id = ?",
        [item_name.trim(), category_id]
      );

      let stockId;
      if (existingStock.length > 0) {
        stockId = existingStock[0].stock_id;

        // Update stock with new values
        await db.execute(
          "UPDATE stock SET quantity = ?, price_pu = ? WHERE stock_id = ?",
          [parseInt(quantity), parseFloat(price_pu), stockId]
        );
      } else {
        // Create new stock item
        const [stockResult] = await db.execute(
          "INSERT INTO stock (item_name, category_id, quantity, price_pu) VALUES (?, ?, ?, ?)",
          [item_name.trim(), category_id, parseInt(quantity), parseFloat(price_pu)]
        );
        stockId = stockResult.insertId;
      }

      // Check if rate already exists
      const [existingRate] = await db.execute(
        "SELECT rate_id FROM rates WHERE item_name = ? AND category_id = ?",
        [item_name.trim(), category_id]
      );

      if (existingRate.length > 0) {
        // Update existing rate
        await db.execute(
          "UPDATE rates SET quantity = ?, price_pu = ?, item_id = ? WHERE rate_id = ?",
          [parseInt(quantity), parseFloat(price_pu), stockId, existingRate[0].rate_id]
        );
      } else {
        // Create new rate
        await db.execute(
          "INSERT INTO rates (item_id, item_name, quantity, price_pu, category_id) VALUES (?, ?, ?, ?, ?)",
          [stockId, item_name.trim(), parseInt(quantity), parseFloat(price_pu), category_id]
        );
      }
    }

    res.status(201).json({ message: 'Rate added successfully' });
  } catch (error) {
    console.error("Database error:", error);
    res.status(500).json({ error: error.message || "Internal Server Error" });
  } finally {
    if (db) db.release();
  }
}