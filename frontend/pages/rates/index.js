import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FaPlus, FaEdit, FaTrash, FaUpload, FaDownload, FaSearch, FaFilter } from 'react-icons/fa';
import BackButton from '@/components/BackButton';
import ScrollToTopButton from '@/components/scrollup';
import Modal from '@/components/Modal';
import CategoryManager from '@/components/rates/CategoryManager';
import ItemManager from '@/components/rates/ItemManager';
import FileImporter from '@/components/rates/FileImporter';

const RatesManagement = () => {
  const [activeTab, setActiveTab] = useState('items');
  const [categories, setCategories] = useState([]);
  const [rates, setRates] = useState([]);
  const [filteredRates, setFilteredRates] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [modalType, setModalType] = useState('');
  const [selectedItem, setSelectedItem] = useState(null);
  const [notification, setNotification] = useState({ show: false, message: '', type: '' });

  // Fetch data functions
  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/rates/categories');
      const data = await response.json();
      if (data.success) {
        setCategories(data.categories);
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
      showNotification('Failed to load categories', 'error');
    }
  };

  const fetchRates = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/rates/searchRates');
      const data = await response.json();
      setRates(data);
      setFilteredRates(data);
    } catch (error) {
      console.error('Error fetching rates:', error);
      showNotification('Failed to load rates', 'error');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCategories();
    fetchRates();
  }, []);

  // Filter and search functionality
  useEffect(() => {
    let filtered = rates;

    if (selectedCategory !== 'all') {
      filtered = filtered.filter(rate => rate.category_id.toString() === selectedCategory);
    }

    if (searchTerm) {
      filtered = filtered.filter(rate =>
        rate.item_name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    setFilteredRates(filtered);
  }, [rates, selectedCategory, searchTerm]);

  const showNotification = (message, type) => {
    setNotification({ show: true, message, type });
    setTimeout(() => setNotification({ show: false, message: '', type: '' }), 3000);
  };

  const handleModalOpen = (type, item = null) => {
    setModalType(type);
    setSelectedItem(item);
    setShowModal(true);
  };

  const handleModalClose = () => {
    setShowModal(false);
    setModalType('');
    setSelectedItem(null);
  };

  const handleDataUpdate = () => {
    fetchCategories();
    fetchRates();
    handleModalClose();
  };

  const handleDeleteRate = async (rateId) => {
    if (!confirm('Are you sure you want to delete this rate?')) return;

    try {
      const response = await fetch('/api/rates/deleteRate', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ rate_id: rateId })
      });

      if (response.ok) {
        showNotification('Rate deleted successfully', 'success');
        fetchRates();
      } else {
        throw new Error('Failed to delete rate');
      }
    } catch (error) {
      console.error('Error deleting rate:', error);
      showNotification('Failed to delete rate', 'error');
    }
  };

  const exportRates = async () => {
    try {
      const response = await fetch('/api/rates/export');
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `rates_export_${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      showNotification('Rates exported successfully', 'success');
    } catch (error) {
      console.error('Error exporting rates:', error);
      showNotification('Failed to export rates', 'error');
    }
  };

  return (
    <div className="min-h-screen bg-black text-white">
      <div className="absolute top-4 left-4 z-10">
        <BackButton route="/quotation/home" />
      </div>
      <ScrollToTopButton />

      {/* Notification */}
      {notification.show && (
        <motion.div
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -50 }}
          className={`fixed top-4 right-4 z-50 px-6 py-3 rounded-lg shadow-lg ${
            notification.type === 'success' ? 'bg-green-600' : 'bg-red-600'
          }`}
        >
          {notification.message}
        </motion.div>
      )}

      <div className="container mx-auto px-4 py-20">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <h1 className="text-4xl font-bold mb-4">Rate Management System</h1>
          <p className="text-gray-400">Manage categories, items, and pricing efficiently</p>
        </motion.div>

        {/* Tab Navigation */}
        <div className="flex justify-center mb-8">
          <div className="bg-gray-800 rounded-lg p-1 flex">
            {[
              { id: 'items', label: 'Items & Rates', icon: FaEdit },
              { id: 'categories', label: 'Categories', icon: FaFilter },
              { id: 'import', label: 'Import Data', icon: FaUpload }
            ].map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center px-6 py-3 rounded-md transition-all ${
                  activeTab === tab.id
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-400 hover:text-white hover:bg-gray-700'
                }`}
              >
                <tab.icon className="mr-2" />
                {tab.label}
              </button>
            ))}
          </div>
        </div>

        {/* Tab Content */}
        {activeTab === 'items' && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="space-y-6"
          >
            {/* Controls */}
            <div className="bg-gray-900 rounded-lg p-6">
              <div className="flex flex-wrap gap-4 items-center justify-between">
                <div className="flex gap-4 items-center">
                  <div className="relative">
                    <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Search items..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 pr-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-blue-500"
                    />
                  </div>
                  <select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                    className="px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:border-blue-500"
                  >
                    <option value="all">All Categories</option>
                    {categories.map(cat => (
                      <option key={cat.category_id} value={cat.category_id}>
                        {cat.category_name}
                      </option>
                    ))}
                  </select>
                </div>
                <div className="flex gap-2">
                  <button
                    onClick={() => handleModalOpen('addItem')}
                    className="flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 rounded-lg transition-colors"
                  >
                    <FaPlus className="mr-2" />
                    Add Item
                  </button>
                  <button
                    onClick={exportRates}
                    className="flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-lg transition-colors"
                  >
                    <FaDownload className="mr-2" />
                    Export
                  </button>
                </div>
              </div>
            </div>

            {/* Items Table */}
            <div className="bg-gray-900 rounded-lg overflow-hidden">
              {loading ? (
                <div className="p-8 text-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
                  <p className="mt-4 text-gray-400">Loading rates...</p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-800">
                      <tr>
                        <th className="px-6 py-4 text-left text-sm font-medium text-gray-300 uppercase tracking-wider">
                          Item Name
                        </th>
                        <th className="px-6 py-4 text-left text-sm font-medium text-gray-300 uppercase tracking-wider">
                          Category
                        </th>
                        <th className="px-6 py-4 text-left text-sm font-medium text-gray-300 uppercase tracking-wider">
                          Quantity
                        </th>
                        <th className="px-6 py-4 text-left text-sm font-medium text-gray-300 uppercase tracking-wider">
                          Price (₹)
                        </th>
                        <th className="px-6 py-4 text-left text-sm font-medium text-gray-300 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-700">
                      {filteredRates.length === 0 ? (
                        <tr>
                          <td colSpan="5" className="px-6 py-8 text-center text-gray-400">
                            No rates found. {searchTerm || selectedCategory !== 'all' ? 'Try adjusting your filters.' : 'Add some items to get started.'}
                          </td>
                        </tr>
                      ) : (
                        filteredRates.map((rate) => (
                          <tr key={rate.rate_id} className="hover:bg-gray-800 transition-colors">
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-white">
                              {rate.item_name}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                              {categories.find(cat => cat.category_id === rate.category_id)?.category_name || 'Unknown'}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                              {rate.quantity}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-green-400 font-medium">
                              ₹{parseFloat(rate.price_pu).toFixed(2)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                              <div className="flex space-x-2">
                                <button
                                  onClick={() => handleModalOpen('editItem', rate)}
                                  className="text-blue-400 hover:text-blue-300 transition-colors"
                                  title="Edit"
                                >
                                  <FaEdit />
                                </button>
                                <button
                                  onClick={() => handleDeleteRate(rate.rate_id)}
                                  className="text-red-400 hover:text-red-300 transition-colors"
                                  title="Delete"
                                >
                                  <FaTrash />
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))
                      )}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </motion.div>
        )}

        {/* Categories Tab */}
        {activeTab === 'categories' && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
          >
            <CategoryManager
              categories={categories}
              onUpdate={handleDataUpdate}
              showNotification={showNotification}
            />
          </motion.div>
        )}

        {/* Import Tab */}
        {activeTab === 'import' && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
          >
            <FileImporter
              categories={categories}
              onUpdate={handleDataUpdate}
              showNotification={showNotification}
            />
          </motion.div>
        )}

        {/* Modal */}
        <Modal
          isOpen={showModal}
          onClose={handleModalClose}
          title={modalType === 'addItem' ? 'Add New Item' : 'Edit Item'}
        >
          <ItemManager
            item={selectedItem}
            categories={categories}
            onSave={handleDataUpdate}
            onCancel={handleModalClose}
            showNotification={showNotification}
            isEdit={modalType === 'editItem'}
          />
        </Modal>
      </div>
    </div>
  );
};

export default RatesManagement;
